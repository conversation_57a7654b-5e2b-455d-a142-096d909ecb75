from dataclasses import dataclass, asdict
from datetime import datetime, timezone
from beanie import BulkWriter
from beanie.operators import In, Or, And
from typing import Tuple, Optional
from zoneinfo import ZoneInfo

from src.db.models import Tournament, Player, AutoStartAction, TournamentConfiguration
from src.schemas.responses import TournamentResponse, TournamentDetailsResponse
from src.utils.feature_flags import feature_flags
from src.utils.enums import CurrencyType, PlayerStatus
from src.utils.logging import logger


@dataclass
class TournamentUpdate:
    tournament_id: str
    tournament_name: str
    tournament_name_eng: str
    starting_time: datetime
    late_registration_time: datetime
    seats_per_table: int
    registration_fee: float
    service_fee: float
    game_pool: float
    overlay: float
    currency: str
    registered_count: int
    joined_count: int
    status: int
    mtt_mode: int
    tournament_mode: int
    game_mode: int
    is_satellite_mode: bool
    sign_up_options: str
    app_id: int
    date_updated: datetime
    multi_flight_id: int
    multi_flight_level: int


GAME_POOL_OVERRIDE = 10.0


class TournamentService:
    @staticmethod
    async def get_tournaments(app_id: int) -> list[TournamentResponse]:
        query = [Tournament.app_id == app_id]
        db_tournaments = await TournamentService.get_tournaments_version_aware(query)
        tournaments_ids = [tournament.tournament_id for tournament in db_tournaments]
        players = await Player.find(
            In(Player.table_id, tournaments_ids)
        ).to_list()
        configs = await TournamentConfiguration.find(
            TournamentConfiguration.app_id == app_id,
            In(TournamentConfiguration.tournament_id, tournaments_ids),
        ).to_list()
        tournaments = []
        for db_tournament in db_tournaments:
            config = next((config for config in configs if config.tournament_id == db_tournament.tournament_id), None)
            tournament_players = [player for player in players if player.table_id == db_tournament.tournament_id]

            tournament = TournamentService.build_tournament_response(db_tournament, config, tournament_players)

            tournaments.append(tournament)

        return tournaments

    @staticmethod
    async def get_tournament_by_id(app_id: int, tournament_id: str) -> TournamentResponse | None:
        tournament = await Tournament.find_one(
            Tournament.app_id == app_id, Tournament.tournament_id == tournament_id
        )
        if tournament is None:
            return None

        config = await TournamentConfiguration.find_one(
            TournamentConfiguration.app_id == app_id,
            TournamentConfiguration.tournament_id == tournament_id,
        )

        tournament_players = await Player.find(
            Player.app_id == tournament.app_id,
            Player.table_id == tournament_id
        ).to_list()

        return TournamentService.build_tournament_response(
            tournament,
            config,
            tournament_players
        )

    @staticmethod
    def build_tournament_response(
        tournament: Tournament,
        config: Optional[TournamentConfiguration],
        players: list[Player]
    ) -> TournamentResponse:

        game_pool = config.game_pool if config and config.game_pool > 0 else tournament.game_pool
        overlay_percentage = tournament.registration_fee * len(players) / game_pool if game_pool > 0 else 0
        response = TournamentResponse(
            **tournament.model_dump(),
            amount_of_players=len(players),
            highest_rank=min([p.rank for p in players if p.rank], default=0),
            highest_chips=max([p.chips for p in players], default=0),
            bots_pending=len([p for p in players if p.status == PlayerStatus.PENDING.value]),
            bots_started=len([p for p in players if p.status not in (PlayerStatus.IDLE.value, PlayerStatus.PENDING.value)]),

            adjusted_game_pool=config.game_pool if config else 0,
            adjusted_game_pool_updated_manually=config.game_pool_updated_manually if config else False,
            scheduling_min_delay_sec=config.scheduling_min_delay_sec if config else None,
            scheduling_max_delay_sec=config.scheduling_max_delay_sec if config else None,
            overlay_percentage=overlay_percentage
        )

        return response

    @staticmethod
    async def get_tournament_details(app_id: int, tournament_id: str) -> TournamentDetailsResponse:
        db_players = await Player.find(
            Player.app_id == app_id, Player.table_id == tournament_id
        ).to_list()
        db_actions = await AutoStartAction.find(
            AutoStartAction.app_id == app_id, AutoStartAction.tournament_id == tournament_id
        ).to_list()
        players = [player.to_short_response() for player in db_players] if db_players else []
        actions = [action.to_response() for action in db_actions] if db_actions else []
        tournament = TournamentDetailsResponse(
            tournament_id=tournament_id,
            players=players,
            actions=actions,
        )
        return tournament

    @staticmethod
    async def get_tournaments_version_aware(query: list) -> list[TournamentResponse]:
        # Get latest date_updated from the tournaments table
        latest_tournament = await TournamentService.get_latest_tournament()
        if latest_tournament:
            date_updated = latest_tournament.date_updated
            query.append(Tournament.date_updated == date_updated.replace(tzinfo=ZoneInfo("UTC")))
        # Tournament query
        filtered_tournaments = await Tournament.find(*query).to_list()
        logger.info(
            "TournamentService.get_tournaments_version_aware",
            f"Found {len(filtered_tournaments)} tournaments with query: {query}",
        )
        return filtered_tournaments

    @staticmethod
    async def get_latest_tournament() -> Tournament | None:
        latest_tournament = (
            await Tournament.find().sort("-date_updated").first_or_none()
        )
        return latest_tournament

    @staticmethod
    async def drop_and_insert_tournaments(tournaments_updates: list[TournamentUpdate], date_updated: datetime):
        """
        We expect that the tournaments_updates list contains all tournaments for corresponding app_ids.
        """
        updated_app_ids = [tournament_update.app_id for tournament_update in tournaments_updates]

        tournaments = [
            Tournament(**asdict(tournament_update)) for tournament_update in tournaments_updates
        ]
        await Tournament.insert_many(tournaments)
        logger.info(
            "TournamentService.drop_and_insert_tournaments",
            f"Inserted {len(tournaments)} tournaments",
        )
        await Tournament.find(
            In(Tournament.app_id, updated_app_ids),
            Tournament.date_updated < date_updated
        ).delete()

    @staticmethod
    async def update_tournament_configurations(
        app_id: int,
        tournament_ids: list[str],
        adjusted_game_pool: float | None,
        adjusted_game_pool_updated_manually: bool,
        scheduling_min_delay_sec: int | None = None,
        scheduling_max_delay_sec: int | None = None,
    ):
        name = "TournamentService.update_tournament_configurations"

        existing_configs = await TournamentConfiguration.find(
            TournamentConfiguration.app_id == app_id,
            In(TournamentConfiguration.tournament_id, tournament_ids),
        ).to_list()

        tournaments_with_config = [config.tournament_id for config in existing_configs]
        tournaments_without_config = [id for id in tournament_ids if id not in tournaments_with_config]

        async with BulkWriter() as bulk_writer:
            for config in existing_configs:
                # Update only provided fields to avoid overwriting with None
                if adjusted_game_pool is not None:
                    config.game_pool = adjusted_game_pool
                    config.game_pool_updated_manually = adjusted_game_pool_updated_manually
                if scheduling_min_delay_sec is not None:
                    config.scheduling_min_delay_sec = scheduling_min_delay_sec
                if scheduling_max_delay_sec is not None:
                    config.scheduling_max_delay_sec = scheduling_max_delay_sec

                config.date_updated = datetime.now(timezone.utc)
                await config.save()

            for tournament_id in tournaments_without_config:
                # Create a new config; if adjusted_game_pool is not provided, default to 0 and False
                await TournamentConfiguration(
                    app_id=app_id,
                    tournament_id=tournament_id,
                    game_pool=adjusted_game_pool if adjusted_game_pool is not None else 0,
                    game_pool_updated_manually=adjusted_game_pool_updated_manually if adjusted_game_pool is not None else False,
                    scheduling_min_delay_sec=scheduling_min_delay_sec,
                    scheduling_max_delay_sec=scheduling_max_delay_sec,
                ).insert()

                logger.info(
                    name,
                    f"Inserted new config for {tournament_id=}, {adjusted_game_pool=}"
                )

            await bulk_writer.commit()

    @staticmethod
    async def auto_adjust_game_pools(tournament_ids: list[str]):
        """
        if tournament.game_pool < 500$ or tournament.registration_fee < 2$, set adjusted_game_pool to 10
        values are set up in feature flags
        """
        name = "TournamentService.auto_adjust_game_pools"

        GAME_POOL_THRESHOLD_USD = feature_flags.get_flag("manager-tournament-auto-adjust-threshold-usd", 499.0)
        GAME_POOL_THRESHOLD_GOLD = feature_flags.get_flag("manager-tournament-auto-adjust-threshold-gold", 3499.0)

        REG_FEE_THRESHOLD_USD = feature_flags.get_flag("manager-tournament-auto-adjust-regfee-threshold-usd", 2.0)
        REG_FEE_THRESHOLD_GOLD = feature_flags.get_flag("manager-tournament-auto-adjust-regfee-threshold-gold", 16.0)

        tournaments = await Tournament.find(
            In(Tournament.tournament_id, tournament_ids),
            Or(
                And(
                    Tournament.game_pool <= GAME_POOL_THRESHOLD_GOLD,
                    Tournament.currency == CurrencyType.GOLD.value
                ),
                And(
                    Tournament.game_pool <= GAME_POOL_THRESHOLD_USD,
                    Tournament.currency == CurrencyType.USD.value
                ),
                And(
                    Tournament.registration_fee <= REG_FEE_THRESHOLD_GOLD,
                    Tournament.currency == CurrencyType.GOLD.value
                ),
                And(
                    Tournament.registration_fee <= REG_FEE_THRESHOLD_USD,
                    Tournament.currency == CurrencyType.USD.value
                ),
            )
        ).to_list()
        existing_configs = await TournamentConfiguration.find(
            In(TournamentConfiguration.tournament_id, tournament_ids),
        ).to_list()

        async with BulkWriter() as bulk_writer:
            for tournament in tournaments:
                config = next(
                    (config for config in existing_configs if config.tournament_id == tournament.tournament_id),
                    None
                )
                if config:
                    if config.game_pool_updated_manually or config.game_pool == GAME_POOL_OVERRIDE:
                        continue

                    config.game_pool = GAME_POOL_OVERRIDE
                    config.date_updated = datetime.now(timezone.utc)
                else:
                    config = TournamentConfiguration(
                        app_id=tournament.app_id,
                        tournament_id=tournament.tournament_id,
                        game_pool=GAME_POOL_OVERRIDE,
                    )
                await config.save()

                logger.info(name, f"Updated config for {tournament.tournament_id=}: "
                            f"{tournament.game_pool=}{tournament.currency} -> {config.game_pool=}, "
                            f"{tournament.registration_fee=}{tournament.currency}")

            await bulk_writer.commit()

    @staticmethod
    async def get_adjusted_game_pool(tournament: Tournament) -> Tuple[float, bool]:
        result = await TournamentConfiguration.find_one(
            TournamentConfiguration.app_id == tournament.app_id,
            TournamentConfiguration.tournament_id == tournament.tournament_id,
        )
        if result is None:
            return 0.0, False

        return result.game_pool, result.game_pool_updated_manually

    @staticmethod
    async def get_adjusted_game_pool_multiple(
        app_id: int, tournament_ids: list[str]
    ) -> list[TournamentConfiguration]:
        return await TournamentConfiguration.find(
            TournamentConfiguration.app_id == app_id,
            In(TournamentConfiguration.tournament_id, tournament_ids),
        ).to_list()
