import { pb as world_pb } from '../../proto/ws_protocol';
import cv from '../cv';

class RemarkData {
    nUid: number = 0;
    nType: number = 0;
    sRemark: string = '';
    nickname: string = '';
    avatar: string = '';
    plat: number = 0;
}

class ServerInfo {
    data: string = '';
    api: string = '';
    qiniu: string = '';
    h5: string = '';
    invalid: boolean;
}

export class Pokerdata {
    Total_win_money: number = 0;
    Total_hand_card_count: number = 0;
    Vpip_rate: number = 0;
    Win_rate: number = 0;
    level_hands: number = 0;
    Pfr_rate: number = 0;
    Af_rate: number = 0;
    Sb_rate: number = 0;
    Etf_rate: number = 0;
    Wsf_rate: number = 0;
    Wtsd_rate: number = 0;
    Wsd_rate: number = 0;
    Rate_3bet: number = 0;
    Rate_fold_to_3bet: number = 0;
    Cbet_rate: number = 0;
    Total_enter_game_count: number = 0;
    Total_end_room_count: number = 0;
    Total_buyin: number = 0;
    Enter_rate: number = 0;
    star_duration: number = 0;
    liked_count: number = 0; //被赞数
    has_liked: boolean = false; //表示可否点赞
    intimacy: number = 0; //亲密度

    Fight_100: number = 0;
    Fight_average: number = 0; //场均战绩
    Buyin_average: number = 0; //场均带入
    UID: number = 0;
    Bb100s: Array<Bb100Info> = [];
}

export class Bb100Info {
    bb_value: number = 0;
    total_win_bb_count: number = 0;
    bb_100: number = 0;
}

class RankData {
    uid: number = 0;
    name: string = '';
    head: string = '';
    updateAt: number = 0; // 更新时间
    rank: number = 0; // -1表示不在榜
    profit: number = 0; // 盈利值
    coin: number = 0;
    frequency: number = 0; // 在连胜榜是 连胜次数, 在其他榜此值为0
    plat: number = 0; // 玩家平台, 有可能没有下发这个字段(老数据没有这个字段)
}

export class UserData {
    public user_id: string = null;
    public u32Uid: number = null; //parseInt(user_id)
    public user_token: string = '';
    public secretKey: string = '';
    public areaCode: number = null;
    public user_ip: string = null;
    public user_safe: number = null;
    public mobile: string = null;
    //是否为游客
    public isTouristUser: boolean = false;
    //是否进入升级账号模式。
    public isOpenUpdateUserMode: boolean = false;
    public avatar: number = null;
    public avatar_thumb: number = null;
    public nick_name: string = null;
    public gender: number = 1;
    public user_marks: string = null;
    public user_area: number = null;
    public diamond_num: number = 0;
    public u32Chips: number = 0;
    public u32Deposit_gold: number = 0;
    public total_amount: number = 0;
    public usdt: number = 0;
    public deposit_usdt: number = 0;
    public priorityareaCode: string = '';
    public prioritymobile: string = '';
    public game_coin: number = 0;
    public user_points: number = 0;
    public points_ratio: number = 1000;
    public games_max: number = null;
    public clubs_max: number = null;
    public current_games: number = null;
    public current_clubs: number = null;
    public reg_code: number = null;
    public u32CardType: number = null;
    public card_expire: number = null;
    public login_server_index = 0;
    public shopUrl: string = '';
    public vipTool_url: string = '';
    public pay_type: number = 0;
    public button_1: string = '';
    public button_1_english: string = '';
    public button_2: string = '';
    public button_2_english: string = '';
    public button_3: string = '';
    public button_3_english: string = '';
    /**
     * 语音上传地址
     */
    public file_upload_url: string = '';

    public is_allow_update_name: boolean = false; //用戶名是否可以修改
    public invitation_code: string = null; //未赋值
    public deviceInfo: string = '';

    public m_bIsNewRegisted: boolean = false;
    public u64DelayBegin: number = 0; //计算信号
    public u64DelayEnd: number = 0; //计算信号

    public headUrl: string = '';
    public HeadPath: string = '';
    public user_areacode: string = '+86';

    public doMain: Array<ServerInfo> = [];
    public server_index: number = 0; //客户端自用
    public server_reconnect_num: number; //客户端自用

    public firstClubId: number = null;
    public firstAlliId: number = null;

    public isEncrypt: number[] = []; //需要加密的游戏id数组
    public CustomUrl: string = '';

    public vRemarkData = new Map<number, RemarkData>();
    public m_totalBuyOut: number = 0;
    public login_server_list: Array<string> = [];

    public isFirstLogin: boolean = true;

    public isfirst: number = null; //是否首次免费
    public isgoldenough: number = null; //金币是否够 1是2否
    public chargefee: number = null; //收取费用, 免费为0
    public freecounts: number = null; //剩余免费次数
    public auditGameuuid: string = ''; //举报的gameUiid

    public m_bIsLoginGameServerSucc: boolean = false;
    public club_head: string = ''; //第一社区头像
    public isvpn: boolean = false; //是否是vpn true是vpn
    public isban: boolean = false; //是否禁止德州人员
    public isBanDelay: boolean = false; //玩家已经被封号，判断是否在退出牌桌场景时要被踢出
    public isallowsimulator: boolean = false;
    public download_url: string = ''; //强制升级地址
    public m_rankInfos: Array<RankData> = [];
    public m_rank: RankData;
    public isShowLuckTurntables: boolean = false; //幸运转盘活动显示状态
    public bk_img: string = ''; //大厅logo下载地址
    public mark_edit_state: number = 0; //签名修改状态 0-可以修改 >0 不能修改 1-修改次数达到上限  2-修入敏感字符达到上限

    public cur_system_time: number = 0; //当前系统时间戳
    public calm_down_deadline_time: number = 0; //小游戏冷静到什么状态

    //kyc验证状态
    public KYCVerificationStatus: string = '';

    totalHands: number = 0;
    bGetTHands: boolean = false;

    // 拉霸相关数据
    hand_num: number = 0;
    luckdrawslen: number = 0;
    playerHands: number[] = [];

    shortDeckFlags: world_pb.FeatureFlags = null;
    diamondLobbyFlag: world_pb.ResponseBoardVisibleSwitch = null;
    ReferralsList: world_pb.ReferralsItem[] = [];
    ReferralsTotal: number;
    maxReferralsMember: number;
    ReferralsPageNum: number;
    summaryInfo: world_pb.GetInviteSummaryResponse;

    SafeDetailList: world_pb.StrongboxDetail[] = [];

    luckindex: number = 0;
    lucks: any[] = [];
    luck_redbags: any[] = [];
    luckTurntables: any[] = [];
    luckTurntablesEndTime: number = 0;
    luckTurntablesStartTime: number = 0;
    luckTurntablesInfo: any = null;
    lamp_list: any[] = [];
    record_list: any[] = [];

    RedBagOpen: boolean = false;
    RedNew: boolean = false;
    rdb_id: number = 0;
    listen_amount: number = 0;
    content: string = '';
    title: string = '';
    leiNum: number = 0;
    packetNum: number = 0;
    red_templets: world_pb.RedBagTemplet[] = [];
    redbags: world_pb.RedBagInfo[] = [];
    history: world_pb.RedBagDrawHistory[] = [];
    jpAmounts: world_pb.RedBagJackpotAmount[] = [];

    RedBagHistory: world_pb.RedBagHistoryResponse = null;
    redPacketInfo: world_pb.RedBagDrawResponse = null;
    redPacketState: world_pb.RedBagStatusResponse = null;
    lastInfo: world_pb.LastRedbagInfoResponse = null;
    autoInfo: world_pb.AutoRedBagDrawResponse = null;
    redPacketJp: world_pb.RedBagJackpotInfoResponse = null;
    boom2Creater: world_pb.NotifyRedBagBoom2Creater = null;
    redPacketTj: world_pb.RedbagStatisticsInfoResponse = null;
    redToCreateData: world_pb.DrawedRedBag2CreatorNotice = null;
    mtt_url: string = '';
    mtt_token: string = '';

    //记录重连时的信息

    public updateRedPacketStatus(id: number, status: number) {
        for (let i = 0; i < this.redbags.length; i++) {
            if (this.redbags[i].rdb_id == id) {
                this.redbags[i].status = status;
            }
        }
    }
    public updateRedPacketIsdrawed(id: number, isdrawed: boolean) {
        for (let i = 0; i < this.redbags.length; i++) {
            if (this.redbags[i].rdb_id == id) {
                this.redbags[i].is_drawed = isdrawed;
            }
        }
    }
    public updateRedPacketJackPot(level: number, amountjP: number) {
        for (let i = 0; i < this.jpAmounts.length; i++) {
            if (this.jpAmounts[i].amount_level == level) {
                this.jpAmounts[i].jackpot_amount = amountjP;
                break;
            }
        }
    }
    getRedPacket(id: number): world_pb.RedBagInfo {
        for (let i = 0; i < this.redbags.length; i++) {
            if (this.redbags[i].rdb_id == id) {
                return this.redbags[i];
            }
        }
        return null;
    }
    getRedTemp(amount: number): world_pb.RedBagTemplet {
        for (let i = 0; i < this.red_templets.length; i++) {
            if (this.red_templets[i].amount == amount) {
                return this.red_templets[i];
            }
        }
        return null;
    }
    public pokerdata: Pokerdata = new Pokerdata();

    addRemark(uid: number, type: number, remark: string, nickname: string, avatar: string, plat: number) {
        let re = new RemarkData();
        re.nType = type;
        re.nUid = uid;
        re.sRemark = remark;
        re.nickname = nickname;
        re.avatar = avatar;
        re.plat = plat;
        this.vRemarkData.set(uid, re);
    }

    getAllRemark(): RemarkData[] {
        let list: RemarkData[] = [];
        cv.dataHandler.getUserData().vRemarkData.forEach((v) => {
            if (v.nType != 0 || (v.sRemark != null && v.sRemark.length > 0)) {
                list.push(v);
            }
        });
        return list;
    }

    getRemarkData(uid: number): RemarkData {
        let result: RemarkData = this.vRemarkData.get(uid);
        if (!result) {
            result = new RemarkData();
            result.nUid = uid;
            this.vRemarkData.set(uid, result);
        }
        return result;
    }

    removeRemarks(uids: number[]) {
        for (let i = 0; i < uids.length; i++) {
            cv.dataHandler.getUserData().vRemarkData.delete(uids[i]);
        }
    }

    getUserRemark(uid: number): string {
        let msg: RemarkData = this.getRemarkData(uid);
        if (msg) {
            return msg.sRemark;
        } else {
            return '';
        }
    }

    /**
     * 通过指定的平台获取完整图片地址
     * @param name      后缀名
     * @param platform  平台值(默认: 0, 其他值请参考接口具体实现的注释)
     */
    getImageUrlByPlat(name: string, platform: number = 0): string {
        return '';
    }

    getPlayerHands(): number {
        let arr = cv.dataHandler.getUserData().playerHands;
        let hand_num = cv.dataHandler.getUserData().hand_num;
        let playerHands = arr[0];
        let len = arr.length;
        for (let i = 0; i < len; ++i) {
            if (hand_num <= arr[i]) {
                playerHands = arr[i];
                break;
            }
        }

        return playerHands;
    }
}
