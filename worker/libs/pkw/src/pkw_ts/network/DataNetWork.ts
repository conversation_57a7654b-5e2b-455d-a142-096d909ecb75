import { pb as world_pb } from '../../proto/ws_protocol';
import { data_proto as data_pb } from '../../proto/data';
import { gate_proto } from '../../proto/gate';

import { NetWorkProxy } from './NetWorkProxy';
import { inflate } from 'pako';

import cv from '../cv';
import { featureFlagValue, logging } from 'shared';

export class DataNetWork extends NetWorkProxy {
    public static instance: DataNetWork;
    private arr = new Map<number, number>();
    public static getInstance(): DataNetWork {
        if (!this.instance) {
            this.instance = new DataNetWork();
            this.instance.init();
        }
        return this.instance;
    }

    public sendGameMsg(
        pbbuf: any,
        msgid: number,
        Roomid: number,
        ServerType: number = cv.Enum.SeverType.SeverType_RANK,
        ServerId: number = world_pb.GameId.DataServer,
    ): boolean {
        return this.sendMsg(pbbuf, msgid, Roomid, ServerType, ServerId);
    }
    public registerMsg(msgid: number, fn: any): void {
        this.registerMessage(msgid, fn, cv.Enum.GameId.Data);
    }
    public init() {
        this.arr.set(data_pb.CMD.GET_DATA_REQ, data_pb.CMD.GET_DATA_RESP);
        this.arr.set(data_pb.CMD.GET_PUBLIC_DATA_REQ, data_pb.CMD.GET_PUBLIC_DATA_RESP);
        this.arr.set(data_pb.CMD.HOME_REQ, data_pb.CMD.HOME_RESP);
        this.arr.set(data_pb.CMD.ROOM_RECORDS_LIST_REQ, data_pb.CMD.ROOM_RECORDS_LIST_RESP);
        this.arr.set(data_pb.CMD.ROOM_RECORD_REQ, data_pb.CMD.ROOM_RECORD_RESP);
        this.arr.set(data_pb.CMD.GAME_UUIDS_REQ, data_pb.CMD.GAME_UUIDS_RESP);
        this.arr.set(data_pb.CMD.GAME_HAND_REQ, data_pb.CMD.GAME_HAND_RESP);
        this.arr.set(data_pb.CMD.GAME_HAND_TEST_REQ, data_pb.CMD.GAME_HAND_TEST_RESP);
        this.arr.set(data_pb.CMD.DO_FAVORITE_REQ, data_pb.CMD.DO_FAVORITE_RESP);
        this.arr.set(data_pb.CMD.FAVORITE_HAND_REQ, data_pb.CMD.FAVORITE_HAND_RESP);
        this.arr.set(data_pb.CMD.FAVORITE_LIST_NEW_REQ, data_pb.CMD.FAVORITE_LIST_NEW_RESP);
        this.arr.set(data_pb.CMD.GAME_REVIEW_LIST_REQ, data_pb.CMD.GAME_REVIEW_LIST_RESP);
        this.arr.set(data_pb.CMD.DELETE_FAVORITE_LIST_REQ, data_pb.CMD.DELETE_FAVORITE_LIST_RESP);
        this.arr.set(data_pb.CMD.FORCE_SHOW_CARD_REQ, data_pb.CMD.FORCE_SHOW_CARD_RSP);
        this.arr.set(data_pb.CMD.SEND_CARD_FUN_REQ, data_pb.CMD.SEND_CARD_FUN_RSP);
        this.arr.set(data_pb.CMD.GAME_BIG_POT_LIST_REQ, data_pb.CMD.GAME_BIG_POT_LIST_RSP);
        this.arr.set(data_pb.CMD.GET_BIG_BLIND_REQ, data_pb.CMD.GET_BIG_BLIND_RESP);
        this.arr.set(data_pb.CMD.GET_HAS_BUYIN_REQ, data_pb.CMD.GET_HAS_BUYIN_RESP);
        this.arr.set(data_pb.CMD.GET_ROUND_INFO_REQ, data_pb.CMD.GET_ROUND_INFO_RESP);
        this.arr.set(data_pb.CMD.GET_UID_HAND_COUNT_REQ, data_pb.CMD.GET_UID_HAND_COUNT_RESP);
        this.arr.set(data_pb.CMD.GET_HAND_COUNT_REQ, data_pb.CMD.GET_HAND_COUNT_RESP);
        this.arr.set(data_pb.CMD.GET_PLAYER_LATEST_REQ, data_pb.CMD.GET_PLAYER_LATEST_RESP);
        this.arr.set(data_pb.CMD.JF_GAME_HAND_REQ, data_pb.CMD.JF_GAME_HAND_RESP);
        this.arr.set(data_pb.CMD.JF_ROOM_LIST_REQ, data_pb.CMD.JF_ROOM_LIST_RESP);
        this.arr.set(data_pb.CMD.JF_GAME_UUIDS_REQ, data_pb.CMD.JF_GAME_UUIDS_RESP);
        this.arr.set(data_pb.CMD.JF_DATA_REQ, data_pb.CMD.JF_DATA_RESP);
        this.arr.set(data_pb.CMD.SUBMIT_HAND_RECORD_REQ, data_pb.CMD.SUBMIT_HAND_RECORD_RSP);
        this.arr.set(
            data_pb.CMD.SUBMIT_HAND_RECORD_MATCHED_RULE_REQ,
            data_pb.CMD.SUBMIT_HAND_RECORD_MATCHED_RULE_RSP,
        );

        this.registerMsg(gate_proto.CMD.SERVER_EXCEPT_NOTIFY, (puf: any) => {
            if (featureFlagValue('log-server-exception', {}, false)) {
                logging.error('Server exception received', puf);
            }
        });
    }

    RequestGetData(id: number, message: any, callback: Function, isZip: boolean = false): void {
        let recvId = this.arr.get(id);
        let str = JSON.stringify(message);
        let msg = { message: str };
        let puf = this.encodePB('DataMessage', msg);
        this.sendGameMsg(puf, id, 0);
        let func = function (buf: any) {
            let msg = this.decodePB('DataMessage', buf);
            if (msg) {
                let value;
                if (isZip) {
                    value = unzip(msg.message);
                    value = JSON.parse(value);
                } else {
                    value = JSON.parse(msg.message);
                }
                callback(value);
            }
        }.bind(this);
        this.registerMsg(recvId, func);
    }
}

function unzip(response: string): string {
    let strData = window.atob(response);
    // Convert binary string to character-number array
    let charData = strData.split('').map(function (x) {
        return x.charCodeAt(0);
    });
    // Turn number array into byte-array
    let binData = new Uint8Array(charData);
    // unzip
    response = inflate(binData, { to: 'string' });
    // Convert gunzipped byteArray back to ascii string:
    //respone = String.fromCharCode.apply(null, new Uint16Array(data));
    return response;
}
