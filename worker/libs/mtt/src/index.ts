import { UnrecoverableError } from 'bullmq';
import { MttConfigType, JobDataHandler, logging, Ticket } from 'shared';
import { scanAndFetch } from './scanning';
import { HttpApis } from './httpApis';
import { signupAndPlay } from './mtt';
import pbVersion from './mtt/pb/version';
import Long from 'long';

export class MttMain {
    private storeTournamentDetails: (id: number, data: any) => void;
    private fetchTournamentDetails: (id: number) => Promise<any>;
    private urlConfig: MttConfigType;

    static init(
        storeTournamentDetails: (id: number, data: any) => void,
        fetchTournamentDetails: (id: number) => Promise<any>,
        protoVersion: string,
        urlConfig: MttConfigType,
    ) {
        mtt.storeTournamentDetails = storeTournamentDetails;
        mtt.fetchTournamentDetails = fetchTournamentDetails;

        pbVersion.version = protoVersion;
        mtt.urlConfig = urlConfig;
    }

    static async scan(token: string, onMessage: JobDataHandler): Promise<void> {
        return scanAndFetch(token, onMessage, mtt.storeTournamentDetails, mtt.urlConfig.mttApi);
    }

    static async play(
        token: string,
        tournamentId: number,
        ticketId: number,
        onMessage: JobDataHandler,
        profileName: string | undefined,
    ) {
        return signupAndPlay(
            token,
            tournamentId,
            ticketId,
            onMessage,
            mtt.fetchTournamentDetails,
            mtt.urlConfig,
            profileName,
        );
    }

    static async check(
        token: string,
        tournamentId: number,
        nickname: string,
        onMessage: JobDataHandler,
    ): Promise<void> {
        const httpApis = new HttpApis(mtt.urlConfig.mttApi);

        // adding alternative way to check if user is registered with fallback to old method
        const response = await httpApis.requestJoinedTournaments(token);
        if (response.ErrorCode) {
            logging.warn('Joined tournaments fetch error', response);
        } else {
            const tournament = response?.MttList?.find((t) => t.TournamentId === tournamentId);
            if (tournament && tournament.JoinStatus > 0) {
                onMessage({ registered: true });
                return;
            }
        }

        const data = await httpApis.requestMttTournamentPlayers(token, tournamentId, nickname);
        if (data.ErrorCode) {
            throw new UnrecoverableError(`Tournament eligibility check error: ${data.ErrorCode}`);
        } else {
            onMessage({ registered: data.PlayersDetail?.length > 0 });
        }
    }

    static async balance(token: string): Promise<Ticket[]> {
        const httpApis = new HttpApis(mtt.urlConfig.mttApi);

        try {
            const data = await httpApis.requestPlayerTicketsData(token);

            const tickets: Ticket[] = (data?.ToolInBackpacks || [])
                .filter((t) => {
                    if (!t.Usable) return false;
                    if (!t.Expiry) return true;
                    const date =
                        t.Expiry instanceof Date
                            ? t.Expiry
                            : new Date(t.Expiry.seconds * 1000 + t.Expiry.nanos / 1000000);
                    return date.getTime() > Date.now();
                })
                .map((t) => {
                    return { tool_id: t.ToolId, ticket_id: Long.isLong(t.Id) ? t.Id.toInt() : t.Id };
                });

            return tickets;
        } catch (error) {
            logging.error('Error fetching player tickets data', error);
        }

        return [];
    }
}

const mtt = new MttMain();
